package com.sgs.gpo.facade.model.todolist;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class ToDoListRsp implements Serializable {
    // todoId
    private String id;
    /**
     * Subcontract/TRF
     */
    private String type;
    // from 信息
    private String sourceOrderNo;
    private String sourceBuCode;
    private String labCode;
    // to信息
    private String toLabCode;
    private String targetBuCode;
    /**
     * 发起方联系人信息
     */
    private String contact;
    private String contactEmail;
    private String contactTel;

    private Date expectDueDate;
    private String pe;
    private String peEmail;
    private Date subContractCreateDate;

    private String serviceType;
    private Integer tat;
    private String cs;
    private String ts;
    private Date sampleReceiveDate;
    private String sampleReceiveBy;
    private String parcelNos;
    private Integer pendFlag;
    /**
     * Manual，Auto
     */
    private String source;
    private String objectNo;
    private String status;
    private Date createdDate;
    private String systemId;

    /**
     * 控制按钮是否可以操作, 默认不展示
     */
    private boolean showPend;
    private boolean showUnPend;
    private boolean showCancel;
    private boolean showReceive;
    private boolean showCreateOrder;
    private boolean showComplete;
}
